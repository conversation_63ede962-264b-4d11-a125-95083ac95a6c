@echo off
echo 启动后台管理系统...
echo.

echo 1. 启动后端服务器...
cd backend
start "Backend Server" cmd /k "npm run dev"
cd ..

echo 2. 等待3秒...
timeout /t 3 /nobreak >nul

echo 3. 启动前端开发服务器...
cd frontend
start "Frontend Server" cmd /k "npm run dev"
cd ..

echo.
echo 系统启动完成！
echo 后端服务器: http://localhost:3000
echo 前端服务器: http://localhost:5173
echo.
echo 测试账号：
echo 超级管理员: admin / 123456
echo 管理员: manager / 123456
echo 普通用户: user / 123456
echo.
pause
