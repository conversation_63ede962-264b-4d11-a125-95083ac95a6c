# 后台管理系统

基于 Vue + Node.js + Element UI + MySQL 的后台管理系统

## 技术栈

### 前端
- Vue 3
- Element Plus
- Vue Router
- Axios
- Vite

### 后端
- Node.js
- Express
- MySQL2
- JWT
- bcryptjs

## 项目结构

```
text/
├── frontend/          # 前端Vue项目
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── router/
│   │   ├── store/
│   │   └── utils/
│   └── package.json
├── backend/           # 后端Node.js项目
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── middleware/
│   │   └── utils/
│   └── package.json
├── database/          # 数据库脚本
│   └── init.sql
└── README.md
```

## 功能模块

- 用户管理：新增、编辑、删除、查询用户
- 角色管理：角色的增删改查
- 权限管理：权限分配和控制
- 登录认证：JWT token认证

## 安装和运行

### 快速开始

1. **安装依赖**
   ```bash
   # Windows用户可以直接运行
   install.bat

   # 或者手动安装
   cd backend && npm install && cd ..
   cd frontend && npm install && cd ..
   ```

2. **配置数据库**
   - 安装MySQL数据库
   - 创建数据库：`CREATE DATABASE admin_system;`
   - 导入初始化脚本：`mysql -u root -p admin_system < database/init.sql`

3. **配置环境变量**
   - 复制 `backend/.env` 文件
   - 修改数据库连接信息：
     ```
     DB_HOST=localhost
     DB_PORT=3306
     DB_USER=root
     DB_PASSWORD=your_password
     DB_NAME=admin_system
     ```

4. **启动系统**
   ```bash
   # Windows用户可以直接运行
   start.bat

   # 或者手动启动
   # 启动后端 (端口3000)
   cd backend && npm run dev

   # 启动前端 (端口5173)
   cd frontend && npm run dev
   ```

5. **访问系统**
   - 前端地址：http://localhost:5173
   - 后端API：http://localhost:3000/api

### 测试账号

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 超级管理员 | admin | 123456 | 所有权限 |
| 管理员 | manager | 123456 | 用户和角色管理 |
| 普通用户 | user | 123456 | 查看权限 |
