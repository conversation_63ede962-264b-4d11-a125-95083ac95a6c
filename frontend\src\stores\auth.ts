import { defineS<PERSON> } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

interface User {
  id: number
  username: string
  email: string
  phone?: string
  real_name?: string
  avatar?: string
  roles: Role[]
  permissions: Permission[]
}

interface Role {
  id: number
  name: string
  description?: string
}

interface Permission {
  id: number
  name: string
  code: string
  description?: string
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)

  const isAuthenticated = computed(() => !!token.value)
  
  const userPermissions = computed(() => {
    return user.value?.permissions?.map(p => p.code) || []
  })

  // 设置token
  function setToken(newToken: string) {
    token.value = newToken
    localStorage.setItem('token', newToken)
    axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`
  }

  // 清除token
  function clearToken() {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    delete axios.defaults.headers.common['Authorization']
  }

  // 登录
  async function login(username: string, password: string) {
    try {
      const response = await axios.post('/auth/login', {
        username,
        password
      })

      if (response.data.success) {
        const { token: newToken, user: userData } = response.data.data
        setToken(newToken)
        user.value = userData
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error: any) {
      const message = error.response?.data?.message || '登录失败'
      return { success: false, message }
    }
  }

  // 登出
  async function logout() {
    try {
      await axios.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearToken()
    }
  }

  // 获取用户信息
  async function fetchUserInfo() {
    try {
      const response = await axios.get('/auth/me')
      if (response.data.success) {
        user.value = response.data.data
        return true
      }
      return false
    } catch (error) {
      console.error('获取用户信息失败:', error)
      clearToken()
      return false
    }
  }

  // 检查权限
  function hasPermission(permission: string): boolean {
    return userPermissions.value.includes(permission)
  }

  // 检查多个权限（需要全部拥有）
  function hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => hasPermission(permission))
  }

  // 检查多个权限（拥有其中一个即可）
  function hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => hasPermission(permission))
  }

  // 初始化
  function init() {
    if (token.value) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    userPermissions,
    setToken,
    clearToken,
    login,
    logout,
    fetchUserInfo,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    init
  }
})
