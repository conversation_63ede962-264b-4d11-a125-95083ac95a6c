<template>
  <div class="roles-page">
    <el-card>
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索角色名称或描述"
            style="width: 300px"
            clearable
            @keyup.enter="loadRoles"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="loadRoles">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
        
        <div class="actions">
          <el-button
            v-if="authStore.hasPermission('role:create')"
            type="primary"
            @click="handleCreate"
          >
            <el-icon><Plus /></el-icon>
            新增角色
          </el-button>
        </div>
      </div>

      <!-- 角色表格 -->
      <el-table
        v-loading="loading"
        :data="roles"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="authStore.hasPermission('role:view')"
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="authStore.hasPermission('role:edit')"
              type="warning"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="authStore.hasPermission('role:edit')"
              type="info"
              size="small"
              @click="handlePermissions(row)"
            >
              权限
            </el-button>
            <el-button
              v-if="authStore.hasPermission('role:delete')"
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRoles"
          @current-change="loadRoles"
        />
      </div>
    </el-card>

    <!-- 角色表单对话框 -->
    <RoleForm
      v-model:visible="formVisible"
      :role="currentRole"
      :mode="formMode"
      @success="handleFormSuccess"
    />

    <!-- 权限分配对话框 -->
    <RolePermissions
      v-model:visible="permissionsVisible"
      :role="currentRole"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import Api from '../utils/api'
import RoleForm from '../components/RoleForm.vue'
import RolePermissions from '../components/RolePermissions.vue'

const authStore = useAuthStore()

// 数据状态
const loading = ref(false)
const roles = ref([])
const searchKeyword = ref('')

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表单状态
const formVisible = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const currentRole = ref(null)

// 权限分配状态
const permissionsVisible = ref(false)

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载角色列表
const loadRoles = async () => {
  try {
    loading.value = true
    const response = await Api.role.getList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    })

    if (response.data.success) {
      roles.value = response.data.data.list
      pagination.total = response.data.data.pagination.total
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增角色
const handleCreate = () => {
  currentRole.value = null
  formMode.value = 'create'
  formVisible.value = true
}

// 查看角色
const handleView = (role: any) => {
  currentRole.value = role
  formMode.value = 'view'
  formVisible.value = true
}

// 编辑角色
const handleEdit = (role: any) => {
  currentRole.value = role
  formMode.value = 'edit'
  formVisible.value = true
}

// 权限管理
const handlePermissions = (role: any) => {
  currentRole.value = role
  permissionsVisible.value = true
}

// 删除角色
const handleDelete = async (role: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await Api.role.delete(role.id)
    if (response.data.success) {
      ElMessage.success('删除成功')
      loadRoles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
    }
  }
}

// 表单操作成功回调
const handleFormSuccess = () => {
  formVisible.value = false
  permissionsVisible.value = false
  loadRoles()
}

onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.roles-page {
  padding: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
