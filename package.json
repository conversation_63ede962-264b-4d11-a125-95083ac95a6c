{"dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "element-plus": "^2.10.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.3", "nodemon": "^3.1.10", "pinia": "^3.0.3", "vue-router": "^4.5.1"}, "name": "text", "version": "1.0.0", "description": "基于 Vue + Node.js + Element UI + MySQL 的后台管理系统", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}