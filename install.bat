@echo off
echo 安装后台管理系统依赖...
echo.

echo 1. 安装后端依赖...
cd backend
npm install
if %errorlevel% neq 0 (
    echo 后端依赖安装失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 2. 安装前端依赖...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo 前端依赖安装失败！
    pause
    exit /b 1
)
cd ..

echo.
echo 依赖安装完成！
echo.
echo 接下来请：
echo 1. 配置MySQL数据库
echo 2. 导入 database/init.sql 文件
echo 3. 修改 backend/.env 文件中的数据库配置
echo 4. 运行 start.bat 启动系统
echo.
pause
