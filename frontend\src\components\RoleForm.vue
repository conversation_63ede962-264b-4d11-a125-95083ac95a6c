<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入角色名称" />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer v-if="mode !== 'view'">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import Api from '../utils/api'

interface Props {
  visible: boolean
  role?: any
  mode: 'create' | 'edit' | 'view'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新增角色'
    case 'edit':
      return '编辑角色'
    case 'view':
      return '查看角色'
    default:
      return '角色信息'
  }
})

// 表单数据
const form = reactive({
  name: '',
  description: '',
  status: 1
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 100, message: '角色名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    status: 1
  })
  formRef.value?.clearValidate()
}

// 填充表单数据
const fillForm = (role: any) => {
  Object.assign(form, {
    name: role.name || '',
    description: role.description || '',
    status: role.status ?? 1
  })
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const submitData = {
      name: form.name,
      description: form.description,
      status: form.status
    }

    let response
    if (props.mode === 'create') {
      response = await Api.role.create(submitData)
    } else {
      response = await Api.role.update(props.role.id, submitData)
    }

    if (response.data.success) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 监听角色数据变化
watch(
  () => props.role,
  (newRole) => {
    if (newRole && (props.mode === 'edit' || props.mode === 'view')) {
      fillForm(newRole)
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.mode === 'create') {
    resetForm()
  }
})
</script>
