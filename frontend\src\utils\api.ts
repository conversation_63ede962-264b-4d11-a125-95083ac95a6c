import axios, { AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// 响应数据接口
interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应接口
interface PageResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: {
    list: T[]
    pagination: {
      total: number
      page: number
      pageSize: number
      totalPages: number
    }
  }
  timestamp: string
}

// 配置axios
axios.defaults.baseURL = 'http://localhost:3000/api'
axios.defaults.timeout = 10000

// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response
  },
  (error) => {
    const authStore = useAuthStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error(data.message || '未授权，请重新登录')
          authStore.clearToken()
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error(data.message || '权限不足')
          break
        case 404:
          ElMessage.error(data.message || '请求的资源不存在')
          break
        case 500:
          ElMessage.error(data.message || '服务器内部错误')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API类
export class Api {
  // 用户相关API
  static user = {
    // 获取用户列表
    getList: (params: { page?: number; pageSize?: number; keyword?: string }) =>
      axios.get<PageResponse>('/users', { params }),
    
    // 获取用户详情
    getById: (id: number) =>
      axios.get<ApiResponse>(`/users/${id}`),
    
    // 创建用户
    create: (data: any) =>
      axios.post<ApiResponse>('/users', data),
    
    // 更新用户
    update: (id: number, data: any) =>
      axios.put<ApiResponse>(`/users/${id}`, data),
    
    // 删除用户
    delete: (id: number) =>
      axios.delete<ApiResponse>(`/users/${id}`)
  }

  // 角色相关API
  static role = {
    // 获取角色列表
    getList: (params: { page?: number; pageSize?: number; keyword?: string }) =>
      axios.get<PageResponse>('/roles', { params }),
    
    // 获取所有角色
    getAll: () =>
      axios.get<ApiResponse>('/roles/all'),
    
    // 获取角色详情
    getById: (id: number) =>
      axios.get<ApiResponse>(`/roles/${id}`),
    
    // 创建角色
    create: (data: any) =>
      axios.post<ApiResponse>('/roles', data),
    
    // 更新角色
    update: (id: number, data: any) =>
      axios.put<ApiResponse>(`/roles/${id}`, data),
    
    // 删除角色
    delete: (id: number) =>
      axios.delete<ApiResponse>(`/roles/${id}`)
  }

  // 权限相关API
  static permission = {
    // 获取权限列表
    getList: (params: { page?: number; pageSize?: number; keyword?: string }) =>
      axios.get<PageResponse>('/permissions', { params }),
    
    // 获取所有权限
    getAll: () =>
      axios.get<ApiResponse>('/permissions/all'),
    
    // 获取权限详情
    getById: (id: number) =>
      axios.get<ApiResponse>(`/permissions/${id}`),
    
    // 创建权限
    create: (data: any) =>
      axios.post<ApiResponse>('/permissions', data),
    
    // 更新权限
    update: (id: number, data: any) =>
      axios.put<ApiResponse>(`/permissions/${id}`, data),
    
    // 删除权限
    delete: (id: number) =>
      axios.delete<ApiResponse>(`/permissions/${id}`)
  }

  // 认证相关API
  static auth = {
    // 登录
    login: (data: { username: string; password: string }) =>
      axios.post<ApiResponse>('/auth/login', data),
    
    // 登出
    logout: () =>
      axios.post<ApiResponse>('/auth/logout'),
    
    // 获取当前用户信息
    getCurrentUser: () =>
      axios.get<ApiResponse>('/auth/me')
  }
}

export default Api
