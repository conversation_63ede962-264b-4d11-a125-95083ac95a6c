<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
      :disabled="mode === 'view'"
    >
      <el-form-item label="权限名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入权限名称" />
      </el-form-item>

      <el-form-item label="权限代码" prop="code">
        <el-input v-model="form.code" placeholder="请输入权限代码，如：user:create" />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入权限描述"
        />
      </el-form-item>
    </el-form>

    <template #footer v-if="mode !== 'view'">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import Api from '../utils/api'

interface Props {
  visible: boolean
  permission?: any
  mode: 'create' | 'edit' | 'view'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新增权限'
    case 'edit':
      return '编辑权限'
    case 'view':
      return '查看权限'
    default:
      return '权限信息'
  }
})

// 表单数据
const form = reactive({
  name: '',
  code: '',
  description: ''
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { min: 2, max: 100, message: '权限代码长度在 2 到 100 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9:_-]+$/, message: '权限代码只能包含字母、数字、冒号、下划线和横线', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    code: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

// 填充表单数据
const fillForm = (permission: any) => {
  Object.assign(form, {
    name: permission.name || '',
    code: permission.code || '',
    description: permission.description || ''
  })
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const submitData = {
      name: form.name,
      code: form.code,
      description: form.description
    }

    let response
    if (props.mode === 'create') {
      response = await Api.permission.create(submitData)
    } else {
      response = await Api.permission.update(props.permission.id, submitData)
    }

    if (response.data.success) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 监听权限数据变化
watch(
  () => props.permission,
  (newPermission) => {
    if (newPermission && (props.mode === 'edit' || props.mode === 'view')) {
      fillForm(newPermission)
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible && props.mode === 'create') {
    resetForm()
  }
})
</script>
