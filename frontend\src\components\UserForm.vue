<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="100px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="form.username" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="真实姓名" prop="real_name">
            <el-input v-model="form.real_name" placeholder="请输入真实姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="form.phone" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'create'">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色" prop="roleIds">
            <el-select
              v-model="form.roleIds"
              multiple
              placeholder="请选择角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in roles"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="头像" prop="avatar">
        <el-input v-model="form.avatar" placeholder="请输入头像URL（可选）" />
      </el-form-item>
    </el-form>

    <template #footer v-if="mode !== 'view'">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        {{ mode === 'create' ? '创建' : '更新' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import Api from '../utils/api'

interface Props {
  visible: boolean
  user?: any
  mode: 'create' | 'edit' | 'view'
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const roles = ref([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 对话框标题
const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新增用户'
    case 'edit':
      return '编辑用户'
    case 'view':
      return '查看用户'
    default:
      return '用户信息'
  }
})

// 表单数据
const form = reactive({
  username: '',
  real_name: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  status: 1,
  avatar: '',
  roleIds: [] as number[]
})

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    username: '',
    real_name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 1,
    avatar: '',
    roleIds: []
  })
  formRef.value?.clearValidate()
}

// 填充表单数据
const fillForm = (user: any) => {
  Object.assign(form, {
    username: user.username || '',
    real_name: user.real_name || '',
    email: user.email || '',
    phone: user.phone || '',
    password: '',
    confirmPassword: '',
    status: user.status ?? 1,
    avatar: user.avatar || '',
    roleIds: user.roles?.map((role: any) => role.id) || []
  })
}

// 加载角色列表
const loadRoles = async () => {
  try {
    const response = await Api.role.getAll()
    if (response.data.success) {
      roles.value = response.data.data
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    const submitData = {
      username: form.username,
      real_name: form.real_name,
      email: form.email,
      phone: form.phone,
      status: form.status,
      avatar: form.avatar,
      roleIds: form.roleIds
    }

    let response
    if (props.mode === 'create') {
      response = await Api.user.create({
        ...submitData,
        password: form.password
      })
    } else {
      response = await Api.user.update(props.user.id, submitData)
    }

    if (response.data.success) {
      ElMessage.success(props.mode === 'create' ? '创建成功' : '更新成功')
      emit('success')
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 监听用户数据变化
watch(
  () => props.user,
  (newUser) => {
    if (newUser && (props.mode === 'edit' || props.mode === 'view')) {
      fillForm(newUser)
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    if (props.mode === 'create') {
      resetForm()
    }
    loadRoles()
  }
})

onMounted(() => {
  loadRoles()
})
</script>
