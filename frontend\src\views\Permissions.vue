<template>
  <div class="permissions-page">
    <el-card>
      <!-- 搜索和操作栏 -->
      <div class="toolbar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索权限名称、代码或描述"
            style="width: 300px"
            clearable
            @keyup.enter="loadPermissions"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="loadPermissions">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
        
        <div class="actions">
          <el-button
            v-if="authStore.hasPermission('permission:manage')"
            type="primary"
            @click="handleCreate"
          >
            <el-icon><Plus /></el-icon>
            新增权限
          </el-button>
        </div>
      </div>

      <!-- 权限表格 -->
      <el-table
        v-loading="loading"
        :data="permissions"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="权限名称" min-width="150" />
        <el-table-column prop="code" label="权限代码" min-width="150">
          <template #default="{ row }">
            <el-tag type="info">{{ row.code }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="authStore.hasPermission('permission:view')"
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="authStore.hasPermission('permission:manage')"
              type="warning"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="authStore.hasPermission('permission:manage')"
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadPermissions"
          @current-change="loadPermissions"
        />
      </div>
    </el-card>

    <!-- 权限表单对话框 -->
    <PermissionForm
      v-model:visible="formVisible"
      :permission="currentPermission"
      :mode="formMode"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import Api from '../utils/api'
import PermissionForm from '../components/PermissionForm.vue'

const authStore = useAuthStore()

// 数据状态
const loading = ref(false)
const permissions = ref([])
const searchKeyword = ref('')

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 表单状态
const formVisible = ref(false)
const formMode = ref<'create' | 'edit' | 'view'>('create')
const currentPermission = ref(null)

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载权限列表
const loadPermissions = async () => {
  try {
    loading.value = true
    const response = await Api.permission.getList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    })

    if (response.data.success) {
      permissions.value = response.data.data.list
      pagination.total = response.data.data.pagination.total
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增权限
const handleCreate = () => {
  currentPermission.value = null
  formMode.value = 'create'
  formVisible.value = true
}

// 查看权限
const handleView = (permission: any) => {
  currentPermission.value = permission
  formMode.value = 'view'
  formVisible.value = true
}

// 编辑权限
const handleEdit = (permission: any) => {
  currentPermission.value = permission
  formMode.value = 'edit'
  formVisible.value = true
}

// 删除权限
const handleDelete = async (permission: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${permission.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await Api.permission.delete(permission.id)
    if (response.data.success) {
      ElMessage.success('删除成功')
      loadPermissions()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
    }
  }
}

// 表单操作成功回调
const handleFormSuccess = () => {
  formVisible.value = false
  loadPermissions()
}

onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
.permissions-page {
  padding: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  gap: 10px;
  align-items: center;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
