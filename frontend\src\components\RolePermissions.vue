<template>
  <el-dialog
    v-model="dialogVisible"
    title="权限分配"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="role-info">
      <h4>角色：{{ role?.name }}</h4>
      <p>{{ role?.description }}</p>
    </div>

    <el-divider />

    <div class="permissions-section">
      <div class="section-header">
        <span>权限列表</span>
        <el-button size="small" @click="toggleSelectAll">
          {{ isAllSelected ? '取消全选' : '全选' }}
        </el-button>
      </div>

      <el-checkbox-group v-model="selectedPermissions" class="permissions-grid">
        <el-checkbox
          v-for="permission in allPermissions"
          :key="permission.id"
          :label="permission.id"
          class="permission-item"
        >
          <div class="permission-content">
            <div class="permission-name">{{ permission.name }}</div>
            <div class="permission-code">{{ permission.code }}</div>
            <div class="permission-desc">{{ permission.description }}</div>
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import Api from '../utils/api'

interface Props {
  visible: boolean
  role?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const allPermissions = ref([])
const selectedPermissions = ref<number[]>([])

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 是否全选
const isAllSelected = computed(() => {
  return allPermissions.value.length > 0 && 
         selectedPermissions.value.length === allPermissions.value.length
})

// 加载所有权限
const loadAllPermissions = async () => {
  try {
    const response = await Api.permission.getAll()
    if (response.data.success) {
      allPermissions.value = response.data.data
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
  }
}

// 加载角色权限
const loadRolePermissions = async () => {
  if (!props.role?.id) return

  try {
    const response = await Api.role.getById(props.role.id)
    if (response.data.success) {
      const rolePermissions = response.data.data.permissions || []
      selectedPermissions.value = rolePermissions.map((p: any) => p.id)
    }
  } catch (error) {
    console.error('加载角色权限失败:', error)
  }
}

// 切换全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedPermissions.value = []
  } else {
    selectedPermissions.value = allPermissions.value.map((p: any) => p.id)
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!props.role?.id) return

  try {
    loading.value = true

    const response = await Api.role.update(props.role.id, {
      name: props.role.name,
      description: props.role.description,
      status: props.role.status,
      permissionIds: selectedPermissions.value
    })

    if (response.data.success) {
      ElMessage.success('权限分配成功')
      emit('success')
    }
  } catch (error) {
    console.error('权限分配失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    loadAllPermissions()
    loadRolePermissions()
  }
})

onMounted(() => {
  loadAllPermissions()
})
</script>

<style scoped>
.role-info {
  padding: 10px 0;
}

.role-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.role-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.permissions-section {
  max-height: 400px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-weight: 600;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 10px;
}

.permission-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s;
  margin: 0;
}

.permission-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.permission-item.is-checked {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.permission-content {
  margin-left: 24px;
}

.permission-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.permission-code {
  font-size: 12px;
  color: #409eff;
  background-color: #ecf5ff;
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  margin-bottom: 4px;
}

.permission-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}
</style>
